# Instagram Reels Archiver

Automatiza o arquivamento de todos os reels de uma conta do Instagram de forma segura e eficiente.

## 🚀 Características

- ✅ Arquiva todos os reels automaticamente
- 🔒 Seguro com rate limiting e tratamento de erros
- 💾 Backup automático dos dados dos reels
- 📊 Progresso salvo para retomar se necessário
- 🔍 Modo "dry run" para testar sem arquivar
- 🎨 Interface colorida e informativa
- 📝 Logs detalhados de todas as operações

## 📋 Pré-requisitos

- Python 3.7 ou superior
- Conta do Instagram
- Conexão com a internet

## 🛠️ Instalação

### Método 1: Setup automático (recomendado)

1. **Clone ou baixe este projeto**
   ```bash
   git clone <url-do-repositorio>
   cd arquivar-posts
   ```

2. **Execute o script de setup**
   ```bash
   python setup.py
   ```

3. **Configure suas credenciais**
   Edite o arquivo `.env` criado automaticamente:
   ```
   INSTAGRAM_USERNAME=seu_usuario_aqui
   INSTAGRAM_PASSWORD=sua_senha_aqui
   ```

### Método 2: Setup manual

1. **Clone ou baixe este projeto**
   ```bash
   git clone <url-do-repositorio>
   cd arquivar-posts
   ```

2. **Instale as dependências**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure suas credenciais**
   ```bash
   cp .env.example .env
   ```

   Edite o arquivo `.env` e adicione suas credenciais:
   ```
   INSTAGRAM_USERNAME=seu_usuario_aqui
   INSTAGRAM_PASSWORD=sua_senha_aqui
   ```

## 🎯 Como usar

### 1. Verificar seus reels (recomendado primeiro)
```bash
python check_reels.py
```
Este comando mostra estatísticas dos seus reels sem fazer alterações.

### 2. Arquivar todos os reels
```bash
python instagram_archiver.py
```

### 3. Desarquivar reels (se necessário)
```bash
python unarchive_reels.py
```
Este comando permite desarquivar reels usando um arquivo de backup.

### Modo de teste (não arquiva realmente)
Edite o arquivo `.env` e defina:
```
DRY_RUN=true
```

Depois execute qualquer um dos comandos acima.

## ⚙️ Configurações

Você pode personalizar o comportamento editando o arquivo `.env`:

| Configuração | Descrição | Padrão |
|-------------|-----------|---------|
| `INSTAGRAM_USERNAME` | Seu nome de usuário do Instagram | **Obrigatório** |
| `INSTAGRAM_PASSWORD` | Sua senha do Instagram | **Obrigatório** |
| `DRY_RUN` | Modo de teste (true/false) | `false` |
| `DELAY_BETWEEN_REQUESTS` | Delay em segundos entre requisições | `2` |
| `MAX_REELS_TO_PROCESS` | Limite de reels a processar (0 = todos) | `0` |
| `BACKUP_ENABLED` | Habilita backup automático (true/false) | `true` |

## 📁 Estrutura de arquivos

```
arquivar-posts/
├── instagram_archiver.py    # Script principal para arquivar
├── unarchive_reels.py      # Script para desarquivar reels
├── check_reels.py          # Script para verificar estatísticas
├── setup.py               # Script de configuração inicial
├── config.py               # Configurações
├── utils.py                # Funções auxiliares
├── requirements.txt        # Dependências
├── .env                   # Suas credenciais (criar)
├── .env.example           # Exemplo de configuração
├── README.md              # Este arquivo
├── backups/               # Backups automáticos (criado automaticamente)
├── logs/                  # Logs das operações (criado automaticamente)
└── progress.json          # Progresso salvo (criado automaticamente)
```

## 🔒 Segurança

- **Nunca compartilhe seu arquivo `.env`** - ele contém suas credenciais
- O script implementa delays entre requisições para evitar rate limiting
- Todas as operações são logadas para auditoria
- Backup automático dos dados antes de arquivar

## 🚨 Avisos importantes

1. **Backup**: O script cria backup automático dos dados dos seus reels antes de arquivar
2. **Reversível**: Reels arquivados podem ser desarquivados manualmente no Instagram
3. **Rate Limiting**: O script respeita os limites do Instagram automaticamente
4. **Progresso**: Se o processo for interrompido, pode ser retomado de onde parou

## 🐛 Solução de problemas

### Erro de login
- Verifique se o nome de usuário e senha estão corretos
- Certifique-se de que não há autenticação de dois fatores ativa
- Tente fazer login manualmente no Instagram primeiro

### Rate limiting
- O script aguarda automaticamente quando necessário
- Se persistir, aumente o `DELAY_BETWEEN_REQUESTS`

### Erro de conexão
- Verifique sua conexão com a internet
- Tente novamente mais tarde

## 📞 Suporte

Se encontrar problemas:

1. Verifique os logs na pasta `logs/`
2. Execute em modo `DRY_RUN=true` primeiro para testar
3. Verifique se todas as dependências estão instaladas

## ⚖️ Disclaimer

Este script é para uso pessoal e educacional. Use por sua própria conta e risco. Respeite os termos de serviço do Instagram.
