import os
import json
import logging
import random
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
from colorama import Fore, Style, init

# Inicializa colorama para cores no terminal
init(autoreset=True)

def setup_logging(log_dir: str = "logs") -> logging.Logger:
    """Configura o sistema de logging"""
    
    # Cria diretório de logs se não existir
    Path(log_dir).mkdir(exist_ok=True)
    
    # Nome do arquivo de log com timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = Path(log_dir) / f"instagram_archiver_{timestamp}.log"
    
    # Configuração do logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Log iniciado: {log_file}")
    
    return logger

def save_backup(data: List[Dict[Any, Any]], backup_dir: str = "backups") -> str:
    """Salva backup dos dados dos reels"""
    
    # Cria diretório de backup se não existir
    Path(backup_dir).mkdir(exist_ok=True)
    
    # Nome do arquivo de backup com timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = Path(backup_dir) / f"reels_backup_{timestamp}.json"
    
    # Salva os dados
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    return str(backup_file)

def load_progress(progress_file: str = "progress.json") -> Dict[str, Any]:
    """Carrega progresso salvo anteriormente"""
    
    if not os.path.exists(progress_file):
        return {"processed_reels": [], "last_processed": None}
    
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"{Fore.YELLOW}Erro ao carregar progresso: {e}")
        return {"processed_reels": [], "last_processed": None}

def save_progress(progress: Dict[str, Any], progress_file: str = "progress.json"):
    """Salva o progresso atual"""
    
    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress, f, indent=2, ensure_ascii=False, default=str)
    except Exception as e:
        print(f"{Fore.RED}Erro ao salvar progresso: {e}")

def print_colored(message: str, color: str = Fore.WHITE):
    """Imprime mensagem colorida"""
    print(f"{color}{message}{Style.RESET_ALL}")

def format_media_info(media) -> Dict[str, Any]:
    """Formata informações do media para backup"""
    return {
        "pk": media.pk,
        "id": media.id,
        "code": media.code,
        "taken_at": media.taken_at.isoformat() if media.taken_at else None,
        "media_type": media.media_type,
        "product_type": media.product_type,
        "caption_text": media.caption_text,
        "like_count": media.like_count,
        "comment_count": media.comment_count,
        "view_count": media.view_count,
        "video_duration": media.video_duration,
        "thumbnail_url": str(media.thumbnail_url) if media.thumbnail_url else None,
        "video_url": str(media.video_url) if media.video_url else None
    }

def smart_delay(config, processed_count: int = 0):
    """
    Implementa delay inteligente para evitar detecção

    Args:
        config: Objeto de configuração
        processed_count: Número de itens já processados
    """
    from config import Config

    # Delay base (aleatório se configurado)
    if hasattr(Config, 'RANDOM_DELAY_MIN') and hasattr(Config, 'RANDOM_DELAY_MAX'):
        base_delay = random.uniform(Config.RANDOM_DELAY_MIN, Config.RANDOM_DELAY_MAX)
        print_colored(f"⏳ Aguardando {base_delay:.1f}s (delay aleatório)...", Fore.YELLOW)
    else:
        base_delay = Config.DELAY_BETWEEN_REQUESTS
        print_colored(f"⏳ Aguardando {base_delay}s...", Fore.YELLOW)

    time.sleep(base_delay)

    # Pausa longa entre lotes (se configurado)
    if hasattr(Config, 'BATCH_SIZE') and hasattr(Config, 'PAUSE_BETWEEN_BATCHES'):
        if processed_count > 0 and processed_count % Config.BATCH_SIZE == 0:
            batch_pause = Config.PAUSE_BETWEEN_BATCHES
            print_colored(f"🛑 Pausa entre lotes: {batch_pause//60}min {batch_pause%60}s", Fore.CYAN)
            time.sleep(batch_pause)

def human_like_delay():
    """
    Simula comportamento humano com delays variados
    """
    # Delays mais naturais baseados em comportamento humano
    delay_patterns = [
        random.uniform(2, 5),    # Rápido (usuário focado)
        random.uniform(5, 10),   # Normal (usuário pensando)
        random.uniform(10, 20),  # Lento (usuário distraído)
        random.uniform(20, 45),  # Muito lento (usuário fazendo outra coisa)
    ]

    # Escolhe padrão baseado em probabilidades
    weights = [0.3, 0.4, 0.2, 0.1]  # 30% rápido, 40% normal, 20% lento, 10% muito lento
    delay = random.choices(delay_patterns, weights=weights)[0]

    print_colored(f"⏳ Delay humano: {delay:.1f}s", Fore.YELLOW)
    time.sleep(delay)

def is_safe_time():
    """
    Verifica se é um horário seguro para automação (simula atividade humana)
    """
    current_hour = datetime.now().hour

    # Horários mais seguros (quando humanos estão mais ativos)
    safe_hours = list(range(8, 23))  # 8h às 23h

    return current_hour in safe_hours

def calculate_estimated_time(total_items: int, config) -> str:
    """
    Calcula tempo estimado baseado nas configurações de delay
    """
    from config import Config

    # Delay médio por item
    if hasattr(Config, 'RANDOM_DELAY_MIN') and hasattr(Config, 'RANDOM_DELAY_MAX'):
        avg_delay = (Config.RANDOM_DELAY_MIN + Config.RANDOM_DELAY_MAX) / 2
    else:
        avg_delay = Config.DELAY_BETWEEN_REQUESTS

    # Pausas entre lotes
    batch_pauses = 0
    if hasattr(Config, 'BATCH_SIZE') and hasattr(Config, 'PAUSE_BETWEEN_BATCHES'):
        num_batches = total_items // Config.BATCH_SIZE
        batch_pauses = num_batches * Config.PAUSE_BETWEEN_BATCHES

    # Tempo total em segundos
    total_seconds = (total_items * avg_delay) + batch_pauses

    # Converte para formato legível
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)

    if hours > 0:
        return f"{hours}h {minutes}min {seconds}s"
    elif minutes > 0:
        return f"{minutes}min {seconds}s"
    else:
        return f"{seconds}s"
