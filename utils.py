import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
from colorama import Fore, Style, init

# Inicializa colorama para cores no terminal
init(autoreset=True)

def setup_logging(log_dir: str = "logs") -> logging.Logger:
    """Configura o sistema de logging"""
    
    # Cria diretório de logs se não existir
    Path(log_dir).mkdir(exist_ok=True)
    
    # Nome do arquivo de log com timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = Path(log_dir) / f"instagram_archiver_{timestamp}.log"
    
    # Configuração do logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Log iniciado: {log_file}")
    
    return logger

def save_backup(data: List[Dict[Any, Any]], backup_dir: str = "backups") -> str:
    """Salva backup dos dados dos reels"""
    
    # Cria diretório de backup se não existir
    Path(backup_dir).mkdir(exist_ok=True)
    
    # Nome do arquivo de backup com timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = Path(backup_dir) / f"reels_backup_{timestamp}.json"
    
    # Salva os dados
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    return str(backup_file)

def load_progress(progress_file: str = "progress.json") -> Dict[str, Any]:
    """Carrega progresso salvo anteriormente"""
    
    if not os.path.exists(progress_file):
        return {"processed_reels": [], "last_processed": None}
    
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"{Fore.YELLOW}Erro ao carregar progresso: {e}")
        return {"processed_reels": [], "last_processed": None}

def save_progress(progress: Dict[str, Any], progress_file: str = "progress.json"):
    """Salva o progresso atual"""
    
    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress, f, indent=2, ensure_ascii=False, default=str)
    except Exception as e:
        print(f"{Fore.RED}Erro ao salvar progresso: {e}")

def print_colored(message: str, color: str = Fore.WHITE):
    """Imprime mensagem colorida"""
    print(f"{color}{message}{Style.RESET_ALL}")

def format_media_info(media) -> Dict[str, Any]:
    """Formata informações do media para backup"""
    return {
        "pk": media.pk,
        "id": media.id,
        "code": media.code,
        "taken_at": media.taken_at.isoformat() if media.taken_at else None,
        "media_type": media.media_type,
        "product_type": media.product_type,
        "caption_text": media.caption_text,
        "like_count": media.like_count,
        "comment_count": media.comment_count,
        "view_count": media.view_count,
        "video_duration": media.video_duration,
        "thumbnail_url": str(media.thumbnail_url) if media.thumbnail_url else None,
        "video_url": str(media.video_url) if media.video_url else None
    }
