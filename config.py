import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente do arquivo .env
load_dotenv()

class Config:
    """Configurações do aplicativo"""
    
    # Credenciais do Instagram
    INSTAGRAM_USERNAME = os.getenv('INSTAGRAM_USERNAME')
    INSTAGRAM_PASSWORD = os.getenv('INSTAGRAM_PASSWORD')
    
    # Configurações opcionais
    DRY_RUN = os.getenv('DRY_RUN', 'false').lower() == 'true'
    DELAY_BETWEEN_REQUESTS = int(os.getenv('DELAY_BETWEEN_REQUESTS', '2'))
    MAX_REELS_TO_PROCESS = int(os.getenv('MAX_REELS_TO_PROCESS', '0'))
    BACKUP_ENABLED = os.getenv('BACKUP_ENABLED', 'true').lower() == 'true'
    
    # Diretórios
    BACKUP_DIR = 'backups'
    LOGS_DIR = 'logs'
    
    @classmethod
    def validate(cls):
        """Valida se as configurações obrigatórias estão definidas"""
        if not cls.INSTAGRAM_USERNAME:
            raise ValueError("INSTAGRAM_USERNAME não está definido no arquivo .env")
        if not cls.INSTAGRAM_PASSWORD:
            raise ValueError("INSTAGRAM_PASSWORD não está definido no arquivo .env")
        
        return True
