#!/usr/bin/env python3
"""
Instagram Reels Deleter
Deleta reels permanentemente (alternativa ao arquivamento)
ATENÇÃO: Esta ação é IRREVERSÍVEL!
"""

import time
import sys
from typing import List

from instagrapi import Client
from tqdm import tqdm
from colorama import Fore

from config import Config
from utils import setup_logging, save_backup, load_progress, save_progress, print_colored, format_media_info


class InstagramDeleter:
    """Classe para deletar reels do Instagram"""
    
    def __init__(self):
        self.client = Client()
        self.logger = setup_logging(Config.LOGS_DIR)
        self.progress = load_progress("delete_progress.json")
    
    def login(self) -> bool:
        """Faz login no Instagram"""
        try:
            print_colored("🔐 Fazendo login no Instagram...", Fore.CYAN)
            
            self.client.login(Config.INSTAGRAM_USERNAME, Config.INSTAGRAM_PASSWORD)
            
            user_info = self.client.account_info()
            print_colored(f"✅ Login realizado com sucesso! Usuário: @{user_info.username}", Fore.GREEN)
            self.logger.info(f"Login realizado para o usuário: {user_info.username}")
            
            return True
            
        except Exception as e:
            print_colored(f"❌ Erro no login: {e}", Fore.RED)
            self.logger.error(f"Erro no login: {e}")
            return False
    
    def get_user_reels(self) -> List:
        """Busca todos os reels do usuário"""
        try:
            user_info = self.client.account_info()
            user_id = str(user_info.pk)
            
            print_colored("📱 Buscando reels...", Fore.CYAN)
            
            reels = self.client.user_clips(user_id, amount=0)
            
            print_colored(f"📊 Encontrados {len(reels)} reels", Fore.BLUE)
            self.logger.info(f"Encontrados {len(reels)} reels para o usuário {user_id}")
            
            return reels
            
        except Exception as e:
            print_colored(f"❌ Erro ao buscar reels: {e}", Fore.RED)
            self.logger.error(f"Erro ao buscar reels: {e}")
            return []
    
    def delete_reel(self, media_pk: int) -> bool:
        """Deleta um reel específico"""
        try:
            if Config.DRY_RUN:
                print_colored(f"🔍 [DRY RUN] Simulando deleção do reel: {media_pk}", Fore.YELLOW)
                return True
            
            result = self.client.media_delete(media_pk)
            
            if result:
                print_colored(f"🗑️ Reel deletado: {media_pk}", Fore.GREEN)
                self.logger.info(f"Reel deletado com sucesso: {media_pk}")
            else:
                print_colored(f"⚠️ Falha ao deletar reel: {media_pk}", Fore.YELLOW)
                self.logger.warning(f"Falha ao deletar reel: {media_pk}")
            
            return result
            
        except Exception as e:
            print_colored(f"❌ Erro ao deletar reel {media_pk}: {e}", Fore.RED)
            self.logger.error(f"Erro ao deletar reel {media_pk}: {e}")
            return False
    
    def delete_all_reels(self):
        """Deleta todos os reels da conta"""
        try:
            # Busca todos os reels
            reels = self.get_user_reels()
            
            if not reels:
                print_colored("ℹ️ Nenhum reel encontrado para deletar", Fore.BLUE)
                return
            
            # Filtra reels já processados
            processed_ids = set(self.progress.get("processed_reels", []))
            reels_to_process = [reel for reel in reels if reel.id not in processed_ids]
            
            if not reels_to_process:
                print_colored("✅ Todos os reels já foram processados!", Fore.GREEN)
                return
            
            # Aplica limite se configurado
            if Config.MAX_REELS_TO_PROCESS > 0:
                reels_to_process = reels_to_process[:Config.MAX_REELS_TO_PROCESS]
            
            print_colored(f"🎯 Processando {len(reels_to_process)} reels...", Fore.CYAN)
            
            # Salva backup se habilitado
            if Config.BACKUP_ENABLED:
                backup_data = [format_media_info(reel) for reel in reels]
                backup_file = save_backup(backup_data, Config.BACKUP_DIR)
                print_colored(f"💾 Backup salvo em: {backup_file}", Fore.BLUE)
                self.logger.info(f"Backup salvo em: {backup_file}")
            
            # Aviso importante
            if not Config.DRY_RUN:
                print_colored("\n" + "="*60, Fore.RED)
                print_colored("⚠️ ATENÇÃO: ESTA AÇÃO É IRREVERSÍVEL!", Fore.RED)
                print_colored("Os reels serão DELETADOS PERMANENTEMENTE!", Fore.RED)
                print_colored("="*60, Fore.RED)
                
                response = input(f"\n{Fore.RED}Digite 'DELETAR' para confirmar: {Fore.WHITE}")
                if response != 'DELETAR':
                    print_colored("❌ Operação cancelada", Fore.YELLOW)
                    return
            
            # Processa cada reel
            successful_deletes = 0
            failed_deletes = 0
            
            with tqdm(total=len(reels_to_process), desc="Deletando reels") as pbar:
                for reel in reels_to_process:
                    try:
                        # Mostra informações do reel
                        caption = reel.caption_text[:50] + "..." if reel.caption_text and len(reel.caption_text) > 50 else reel.caption_text or "Sem legenda"
                        pbar.set_description(f"Processando: {caption}")
                        
                        # Deleta o reel
                        if self.delete_reel(reel.pk):
                            successful_deletes += 1
                            self.progress["processed_reels"].append(reel.id)
                            self.progress["last_processed"] = reel.id
                        else:
                            failed_deletes += 1
                        
                        # Salva progresso
                        save_progress(self.progress, "delete_progress.json")
                        
                        # Delay entre requisições
                        if Config.DELAY_BETWEEN_REQUESTS > 0:
                            time.sleep(Config.DELAY_BETWEEN_REQUESTS)
                        
                        pbar.update(1)
                        
                    except KeyboardInterrupt:
                        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
                        save_progress(self.progress, "delete_progress.json")
                        break
                    except Exception as e:
                        print_colored(f"❌ Erro inesperado ao processar reel {reel.id}: {e}", Fore.RED)
                        self.logger.error(f"Erro inesperado ao processar reel {reel.id}: {e}")
                        failed_deletes += 1
                        pbar.update(1)
            
            # Relatório final
            print_colored("\n" + "="*50, Fore.CYAN)
            print_colored("📊 RELATÓRIO FINAL", Fore.CYAN)
            print_colored("="*50, Fore.CYAN)
            print_colored(f"✅ Reels deletados com sucesso: {successful_deletes}", Fore.GREEN)
            print_colored(f"❌ Reels com falha: {failed_deletes}", Fore.RED)
            print_colored(f"📱 Total de reels encontrados: {len(reels)}", Fore.BLUE)
            
            if Config.DRY_RUN:
                print_colored("🔍 Modo DRY RUN ativo - nenhum reel foi realmente deletado", Fore.YELLOW)
            
            self.logger.info(f"Processo finalizado. Sucessos: {successful_deletes}, Falhas: {failed_deletes}")
            
        except Exception as e:
            print_colored(f"❌ Erro crítico no processo: {e}", Fore.RED)
            self.logger.error(f"Erro crítico no processo: {e}")


def main():
    """Função principal"""
    try:
        Config.validate()
        
        print_colored("🗑️ Instagram Reels Deleter", Fore.RED)
        print_colored("="*40, Fore.RED)
        print_colored("⚠️ ATENÇÃO: Esta ferramenta DELETA reels permanentemente!", Fore.RED)
        
        if Config.DRY_RUN:
            print_colored("🔍 MODO DRY RUN ATIVO - Nenhum reel será realmente deletado", Fore.YELLOW)
        
        # Cria instância do deleter
        deleter = InstagramDeleter()
        
        # Faz login
        if not deleter.login():
            print_colored("❌ Falha no login. Verifique suas credenciais.", Fore.RED)
            sys.exit(1)
        
        # Deleta todos os reels
        deleter.delete_all_reels()
        
        print_colored("\n🎉 Processo concluído!", Fore.GREEN)
        
    except KeyboardInterrupt:
        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"❌ Erro crítico: {e}", Fore.RED)
        sys.exit(1)


if __name__ == "__main__":
    main()
