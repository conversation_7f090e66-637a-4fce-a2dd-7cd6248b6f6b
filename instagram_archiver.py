#!/usr/bin/env python3
"""
Instagram Reels Archiver
Automatiza o arquivamento de todos os reels de uma conta do Instagram
"""

import time
import sys
from typing import List, Optional
from pathlib import Path

from instagrapi import Client
from instagrapi.exceptions import LoginRequired, PleaseWaitFewMinutes, RateLimitError
from tqdm import tqdm
from colorama import Fore, Style

from config import Config
from utils import (
    setup_logging, save_backup, load_progress, save_progress,
    print_colored, format_media_info
)


class InstagramArchiver:
    """Classe principal para arquivar reels do Instagram"""
    
    def __init__(self):
        self.client = Client()
        self.logger = setup_logging(Config.LOGS_DIR)
        self.progress = load_progress()
        
    def login(self) -> bool:
        """Faz login no Instagram"""
        try:
            print_colored("🔐 Fazendo login no Instagram...", Fore.CYAN)
            
            # Tenta fazer login
            self.client.login(Config.INSTAGRAM_USERNAME, Config.INSTAGRAM_PASSWORD)
            
            # Verifica se o login foi bem-sucedido
            user_info = self.client.account_info()
            print_colored(f"✅ Login realizado com sucesso! Usuário: @{user_info.username}", Fore.GREEN)
            self.logger.info(f"Login realizado para o usuário: {user_info.username}")
            
            return True
            
        except LoginRequired as e:
            print_colored(f"❌ Erro de login: {e}", Fore.RED)
            self.logger.error(f"Erro de login: {e}")
            return False
        except Exception as e:
            print_colored(f"❌ Erro inesperado no login: {e}", Fore.RED)
            self.logger.error(f"Erro inesperado no login: {e}")
            return False
    
    def get_user_reels(self, user_id: Optional[str] = None) -> List:
        """Busca todos os reels do usuário"""
        try:
            if not user_id:
                # Usa o próprio usuário logado
                user_info = self.client.account_info()
                user_id = str(user_info.pk)
            
            print_colored("📱 Buscando reels...", Fore.CYAN)
            
            # Busca todos os reels
            reels = self.client.user_clips(user_id, amount=0)  # amount=0 busca todos
            
            print_colored(f"📊 Encontrados {len(reels)} reels", Fore.BLUE)
            self.logger.info(f"Encontrados {len(reels)} reels para o usuário {user_id}")
            
            return reels
            
        except Exception as e:
            print_colored(f"❌ Erro ao buscar reels: {e}", Fore.RED)
            self.logger.error(f"Erro ao buscar reels: {e}")
            return []
    
    def archive_reel(self, media_id: str) -> bool:
        """Arquiva um reel específico"""
        try:
            if Config.DRY_RUN:
                print_colored(f"🔍 [DRY RUN] Simulando arquivamento do reel: {media_id}", Fore.YELLOW)
                return True
            
            # Arquiva o reel
            result = self.client.media_archive(media_id)
            
            if result:
                print_colored(f"📦 Reel arquivado: {media_id}", Fore.GREEN)
                self.logger.info(f"Reel arquivado com sucesso: {media_id}")
            else:
                print_colored(f"⚠️ Falha ao arquivar reel: {media_id}", Fore.YELLOW)
                self.logger.warning(f"Falha ao arquivar reel: {media_id}")
            
            return result
            
        except RateLimitError as e:
            print_colored(f"⏳ Rate limit atingido. Aguardando...", Fore.YELLOW)
            self.logger.warning(f"Rate limit atingido: {e}")
            time.sleep(60)  # Aguarda 1 minuto
            return False
        except PleaseWaitFewMinutes as e:
            print_colored(f"⏳ Instagram solicitou espera. Aguardando...", Fore.YELLOW)
            self.logger.warning(f"Instagram solicitou espera: {e}")
            time.sleep(300)  # Aguarda 5 minutos
            return False
        except Exception as e:
            print_colored(f"❌ Erro ao arquivar reel {media_id}: {e}", Fore.RED)
            self.logger.error(f"Erro ao arquivar reel {media_id}: {e}")
            return False
    
    def archive_all_reels(self):
        """Arquiva todos os reels da conta"""
        try:
            # Busca todos os reels
            reels = self.get_user_reels()
            
            if not reels:
                print_colored("ℹ️ Nenhum reel encontrado para arquivar", Fore.BLUE)
                return
            
            # Filtra reels já processados
            processed_ids = set(self.progress.get("processed_reels", []))
            reels_to_process = [reel for reel in reels if reel.id not in processed_ids]
            
            if not reels_to_process:
                print_colored("✅ Todos os reels já foram processados!", Fore.GREEN)
                return
            
            # Aplica limite se configurado
            if Config.MAX_REELS_TO_PROCESS > 0:
                reels_to_process = reels_to_process[:Config.MAX_REELS_TO_PROCESS]
            
            print_colored(f"🎯 Processando {len(reels_to_process)} reels...", Fore.CYAN)
            
            # Salva backup se habilitado
            if Config.BACKUP_ENABLED:
                backup_data = [format_media_info(reel) for reel in reels]
                backup_file = save_backup(backup_data, Config.BACKUP_DIR)
                print_colored(f"💾 Backup salvo em: {backup_file}", Fore.BLUE)
                self.logger.info(f"Backup salvo em: {backup_file}")
            
            # Processa cada reel
            successful_archives = 0
            failed_archives = 0
            
            with tqdm(total=len(reels_to_process), desc="Arquivando reels") as pbar:
                for reel in reels_to_process:
                    try:
                        # Mostra informações do reel
                        caption = reel.caption_text[:50] + "..." if reel.caption_text and len(reel.caption_text) > 50 else reel.caption_text or "Sem legenda"
                        pbar.set_description(f"Processando: {caption}")
                        
                        # Arquiva o reel
                        if self.archive_reel(reel.id):
                            successful_archives += 1
                            self.progress["processed_reels"].append(reel.id)
                            self.progress["last_processed"] = reel.id
                        else:
                            failed_archives += 1
                        
                        # Salva progresso
                        save_progress(self.progress)
                        
                        # Delay entre requisições
                        if Config.DELAY_BETWEEN_REQUESTS > 0:
                            time.sleep(Config.DELAY_BETWEEN_REQUESTS)
                        
                        pbar.update(1)
                        
                    except KeyboardInterrupt:
                        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
                        save_progress(self.progress)
                        break
                    except Exception as e:
                        print_colored(f"❌ Erro inesperado ao processar reel {reel.id}: {e}", Fore.RED)
                        self.logger.error(f"Erro inesperado ao processar reel {reel.id}: {e}")
                        failed_archives += 1
                        pbar.update(1)
            
            # Relatório final
            print_colored("\n" + "="*50, Fore.CYAN)
            print_colored("📊 RELATÓRIO FINAL", Fore.CYAN)
            print_colored("="*50, Fore.CYAN)
            print_colored(f"✅ Reels arquivados com sucesso: {successful_archives}", Fore.GREEN)
            print_colored(f"❌ Reels com falha: {failed_archives}", Fore.RED)
            print_colored(f"📱 Total de reels encontrados: {len(reels)}", Fore.BLUE)
            
            if Config.DRY_RUN:
                print_colored("🔍 Modo DRY RUN ativo - nenhum reel foi realmente arquivado", Fore.YELLOW)
            
            self.logger.info(f"Processo finalizado. Sucessos: {successful_archives}, Falhas: {failed_archives}")
            
        except Exception as e:
            print_colored(f"❌ Erro crítico no processo: {e}", Fore.RED)
            self.logger.error(f"Erro crítico no processo: {e}")


def main():
    """Função principal"""
    try:
        # Valida configurações
        Config.validate()
        
        print_colored("🚀 Instagram Reels Archiver", Fore.CYAN)
        print_colored("="*40, Fore.CYAN)
        
        if Config.DRY_RUN:
            print_colored("🔍 MODO DRY RUN ATIVO - Nenhum reel será realmente arquivado", Fore.YELLOW)
        
        # Cria instância do archiver
        archiver = InstagramArchiver()
        
        # Faz login
        if not archiver.login():
            print_colored("❌ Falha no login. Verifique suas credenciais.", Fore.RED)
            sys.exit(1)
        
        # Confirma ação
        if not Config.DRY_RUN:
            response = input(f"\n{Fore.YELLOW}⚠️ Tem certeza que deseja arquivar TODOS os seus reels? (s/N): {Style.RESET_ALL}")
            if response.lower() not in ['s', 'sim', 'y', 'yes']:
                print_colored("❌ Operação cancelada pelo usuário", Fore.YELLOW)
                sys.exit(0)
        
        # Arquiva todos os reels
        archiver.archive_all_reels()
        
        print_colored("\n🎉 Processo concluído!", Fore.GREEN)
        
    except KeyboardInterrupt:
        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"❌ Erro crítico: {e}", Fore.RED)
        sys.exit(1)


if __name__ == "__main__":
    main()
