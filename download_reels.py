#!/usr/bin/env python3
"""
Instagram Reels Downloader
Baixa todos os reels para backup local antes de deletar
"""

import time
import sys
import json
from pathlib import Path
from typing import List

from instagrapi import Client
from tqdm import tqdm
from colorama import Fore

from config import Config
from utils import setup_logging, load_progress, save_progress, print_colored, format_media_info


class InstagramDownloader:
    """Classe para baixar reels do Instagram"""
    
    def __init__(self):
        self.client = Client()
        self.logger = setup_logging(Config.LOGS_DIR)
        self.progress = load_progress("download_progress.json")
        self.download_dir = Path("downloads")
        self.download_dir.mkdir(exist_ok=True)
    
    def login(self) -> bool:
        """Faz login no Instagram"""
        try:
            print_colored("🔐 Fazendo login no Instagram...", Fore.CYAN)
            
            self.client.login(Config.INSTAGRAM_USERNAME, Config.INSTAGRAM_PASSWORD)
            
            user_info = self.client.account_info()
            print_colored(f"✅ Login realizado com sucesso! Usuário: @{user_info.username}", Fore.GREEN)
            self.logger.info(f"Login realizado para o usuário: {user_info.username}")
            
            return True
            
        except Exception as e:
            print_colored(f"❌ Erro no login: {e}", Fore.RED)
            self.logger.error(f"Erro no login: {e}")
            return False
    
    def get_user_reels(self) -> List:
        """Busca todos os reels do usuário"""
        try:
            user_info = self.client.account_info()
            user_id = str(user_info.pk)
            
            print_colored("📱 Buscando reels...", Fore.CYAN)
            
            reels = self.client.user_clips(user_id, amount=0)
            
            print_colored(f"📊 Encontrados {len(reels)} reels", Fore.BLUE)
            self.logger.info(f"Encontrados {len(reels)} reels para o usuário {user_id}")
            
            return reels
            
        except Exception as e:
            print_colored(f"❌ Erro ao buscar reels: {e}", Fore.RED)
            self.logger.error(f"Erro ao buscar reels: {e}")
            return []
    
    def download_reel(self, reel) -> bool:
        """Baixa um reel específico"""
        try:
            if Config.DRY_RUN:
                print_colored(f"🔍 [DRY RUN] Simulando download do reel: {reel.pk}", Fore.YELLOW)
                return True
            
            # Cria nome do arquivo
            safe_caption = "".join(c for c in (reel.caption_text or "sem_legenda")[:30] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{reel.pk}_{safe_caption}"
            
            # Baixa o reel
            downloaded_path = self.client.clip_download(reel.pk, folder=self.download_dir)
            
            # Salva metadados
            metadata = format_media_info(reel)
            metadata_file = self.download_dir / f"{reel.pk}_metadata.json"
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            print_colored(f"📥 Reel baixado: {downloaded_path.name}", Fore.GREEN)
            self.logger.info(f"Reel baixado com sucesso: {downloaded_path}")
            
            return True
            
        except Exception as e:
            print_colored(f"❌ Erro ao baixar reel {reel.pk}: {e}", Fore.RED)
            self.logger.error(f"Erro ao baixar reel {reel.pk}: {e}")
            return False
    
    def download_all_reels(self):
        """Baixa todos os reels da conta"""
        try:
            # Busca todos os reels
            reels = self.get_user_reels()
            
            if not reels:
                print_colored("ℹ️ Nenhum reel encontrado para baixar", Fore.BLUE)
                return
            
            # Filtra reels já processados
            processed_ids = set(self.progress.get("processed_reels", []))
            reels_to_process = [reel for reel in reels if reel.id not in processed_ids]
            
            if not reels_to_process:
                print_colored("✅ Todos os reels já foram baixados!", Fore.GREEN)
                return
            
            # Aplica limite se configurado
            if Config.MAX_REELS_TO_PROCESS > 0:
                reels_to_process = reels_to_process[:Config.MAX_REELS_TO_PROCESS]
            
            print_colored(f"🎯 Baixando {len(reels_to_process)} reels...", Fore.CYAN)
            print_colored(f"📁 Pasta de download: {self.download_dir.absolute()}", Fore.BLUE)
            
            # Processa cada reel
            successful_downloads = 0
            failed_downloads = 0
            
            with tqdm(total=len(reels_to_process), desc="Baixando reels") as pbar:
                for reel in reels_to_process:
                    try:
                        # Mostra informações do reel
                        caption = reel.caption_text[:50] + "..." if reel.caption_text and len(reel.caption_text) > 50 else reel.caption_text or "Sem legenda"
                        pbar.set_description(f"Baixando: {caption}")
                        
                        # Baixa o reel
                        if self.download_reel(reel):
                            successful_downloads += 1
                            self.progress["processed_reels"].append(reel.id)
                            self.progress["last_processed"] = reel.id
                        else:
                            failed_downloads += 1
                        
                        # Salva progresso
                        save_progress(self.progress, "download_progress.json")
                        
                        # Delay entre requisições
                        if Config.DELAY_BETWEEN_REQUESTS > 0:
                            time.sleep(Config.DELAY_BETWEEN_REQUESTS)
                        
                        pbar.update(1)
                        
                    except KeyboardInterrupt:
                        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
                        save_progress(self.progress, "download_progress.json")
                        break
                    except Exception as e:
                        print_colored(f"❌ Erro inesperado ao processar reel {reel.id}: {e}", Fore.RED)
                        self.logger.error(f"Erro inesperado ao processar reel {reel.id}: {e}")
                        failed_downloads += 1
                        pbar.update(1)
            
            # Relatório final
            print_colored("\n" + "="*50, Fore.CYAN)
            print_colored("📊 RELATÓRIO FINAL", Fore.CYAN)
            print_colored("="*50, Fore.CYAN)
            print_colored(f"✅ Reels baixados com sucesso: {successful_downloads}", Fore.GREEN)
            print_colored(f"❌ Reels com falha: {failed_downloads}", Fore.RED)
            print_colored(f"📱 Total de reels encontrados: {len(reels)}", Fore.BLUE)
            print_colored(f"📁 Arquivos salvos em: {self.download_dir.absolute()}", Fore.BLUE)
            
            if Config.DRY_RUN:
                print_colored("🔍 Modo DRY RUN ativo - nenhum reel foi realmente baixado", Fore.YELLOW)
            
            self.logger.info(f"Processo finalizado. Sucessos: {successful_downloads}, Falhas: {failed_downloads}")
            
        except Exception as e:
            print_colored(f"❌ Erro crítico no processo: {e}", Fore.RED)
            self.logger.error(f"Erro crítico no processo: {e}")


def main():
    """Função principal"""
    try:
        Config.validate()
        
        print_colored("📥 Instagram Reels Downloader", Fore.CYAN)
        print_colored("="*40, Fore.CYAN)
        
        if Config.DRY_RUN:
            print_colored("🔍 MODO DRY RUN ATIVO - Nenhum reel será realmente baixado", Fore.YELLOW)
        
        # Cria instância do downloader
        downloader = InstagramDownloader()
        
        # Faz login
        if not downloader.login():
            print_colored("❌ Falha no login. Verifique suas credenciais.", Fore.RED)
            sys.exit(1)
        
        # Baixa todos os reels
        downloader.download_all_reels()
        
        print_colored("\n🎉 Processo concluído!", Fore.GREEN)
        
    except KeyboardInterrupt:
        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"❌ Erro crítico: {e}", Fore.RED)
        sys.exit(1)


if __name__ == "__main__":
    main()
