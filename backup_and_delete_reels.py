#!/usr/bin/env python3
"""
Instagram Reels Backup & Delete
Baixa todos os reels para backup local e depois os deleta
Esta é a solução completa para "arquivar" reels (já que o Instagram não permite arquivamento via API)
"""

import time
import sys
from pathlib import Path

from colorama import Fore

from config import Config
from utils import print_colored
from download_reels import InstagramDownloader
from delete_reels import InstagramDeleter


def main():
    """Função principal"""
    try:
        Config.validate()
        
        print_colored("📦 Instagram Reels Backup & Delete", Fore.CYAN)
        print_colored("="*50, Fore.CYAN)
        print_colored("Esta ferramenta:", Fore.WHITE)
        print_colored("1. 📥 Baixa todos os seus reels para backup local", Fore.BLUE)
        print_colored("2. 🗑️ Deleta os reels do Instagram (IRREVERSÍVEL!)", Fore.RED)
        print_colored("", Fore.WHITE)
        
        if Config.DRY_RUN:
            print_colored("🔍 MODO DRY RUN ATIVO - Nenhuma ação real será executada", Fore.YELLOW)
        
        # Confirma ação
        if not Config.DRY_RUN:
            print_colored("⚠️ ATENÇÃO:", Fore.RED)
            print_colored("- Os reels serão baixados para seu computador", Fore.WHITE)
            print_colored("- Depois serão DELETADOS PERMANENTEMENTE do Instagram", Fore.RED)
            print_colored("- Esta ação NÃO PODE ser desfeita!", Fore.RED)
            print_colored("", Fore.WHITE)
            
            response = input(f"{Fore.YELLOW}Deseja continuar? Digite 'CONFIRMO' para prosseguir: {Fore.WHITE}")
            if response != 'CONFIRMO':
                print_colored("❌ Operação cancelada", Fore.YELLOW)
                sys.exit(0)
        
        # Etapa 1: Download dos reels
        print_colored("\n" + "="*50, Fore.CYAN)
        print_colored("📥 ETAPA 1: BAIXANDO REELS", Fore.CYAN)
        print_colored("="*50, Fore.CYAN)
        
        downloader = InstagramDownloader()
        
        if not downloader.login():
            print_colored("❌ Falha no login para download. Verifique suas credenciais.", Fore.RED)
            sys.exit(1)
        
        downloader.download_all_reels()
        
        # Verifica se o download foi bem-sucedido
        downloads_dir = Path("downloads")
        if not Config.DRY_RUN and downloads_dir.exists():
            downloaded_files = list(downloads_dir.glob("*.mp4"))
            if not downloaded_files:
                print_colored("❌ Nenhum arquivo foi baixado. Cancelando deleção por segurança.", Fore.RED)
                sys.exit(1)
            else:
                print_colored(f"✅ {len(downloaded_files)} reels baixados com sucesso!", Fore.GREEN)
        
        # Pausa entre etapas
        if not Config.DRY_RUN:
            print_colored("\n⏳ Aguardando 10 segundos antes da deleção...", Fore.YELLOW)
            time.sleep(10)
        
        # Etapa 2: Deleção dos reels
        print_colored("\n" + "="*50, Fore.RED)
        print_colored("🗑️ ETAPA 2: DELETANDO REELS", Fore.RED)
        print_colored("="*50, Fore.RED)
        
        if not Config.DRY_RUN:
            print_colored("⚠️ ÚLTIMA CHANCE DE CANCELAR!", Fore.RED)
            response = input(f"{Fore.RED}Digite 'DELETAR' para confirmar a deleção: {Fore.WHITE}")
            if response != 'DELETAR':
                print_colored("❌ Deleção cancelada. Seus reels foram baixados mas não deletados.", Fore.YELLOW)
                sys.exit(0)
        
        deleter = InstagramDeleter()
        
        if not deleter.login():
            print_colored("❌ Falha no login para deleção. Seus reels foram baixados mas não deletados.", Fore.RED)
            sys.exit(1)
        
        deleter.delete_all_reels()
        
        # Relatório final
        print_colored("\n" + "="*60, Fore.GREEN)
        print_colored("🎉 PROCESSO COMPLETO FINALIZADO!", Fore.GREEN)
        print_colored("="*60, Fore.GREEN)
        
        if not Config.DRY_RUN:
            print_colored("✅ Seus reels foram:", Fore.WHITE)
            print_colored(f"  📥 Baixados para: {downloads_dir.absolute()}", Fore.BLUE)
            print_colored("  🗑️ Deletados do Instagram", Fore.RED)
            print_colored("", Fore.WHITE)
            print_colored("💡 Dicas:", Fore.YELLOW)
            print_colored("- Faça backup dos arquivos baixados em local seguro", Fore.WHITE)
            print_colored("- Os metadados estão salvos em arquivos *_metadata.json", Fore.WHITE)
            print_colored("- Esta ação não pode ser desfeita", Fore.WHITE)
        else:
            print_colored("🔍 Modo DRY RUN - nenhuma ação real foi executada", Fore.YELLOW)
        
    except KeyboardInterrupt:
        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
        print_colored("⚠️ Verifique o que foi processado nos logs", Fore.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"❌ Erro crítico: {e}", Fore.RED)
        sys.exit(1)


if __name__ == "__main__":
    main()
