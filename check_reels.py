#!/usr/bin/env python3
"""
Instagram Reels Checker
Verifica quantos reels você tem e mostra estatísticas
"""

from instagrapi import Client
from colorama import Fore
from datetime import datetime, timezone

from config import Config
from utils import setup_logging, print_colored


class InstagramChecker:
    """Classe para verificar reels do Instagram"""
    
    def __init__(self):
        self.client = Client()
        self.logger = setup_logging(Config.LOGS_DIR)
    
    def login(self) -> bool:
        """Faz login no Instagram"""
        try:
            print_colored("🔐 Fazendo login no Instagram...", Fore.CYAN)
            
            self.client.login(Config.INSTAGRAM_USERNAME, Config.INSTAGRAM_PASSWORD)
            
            user_info = self.client.account_info()
            print_colored(f"✅ Login realizado com sucesso! Usuário: @{user_info.username}", Fore.GREEN)
            
            return True
            
        except Exception as e:
            print_colored(f"❌ Erro no login: {e}", Fore.RED)
            return False
    
    def check_reels_stats(self):
        """Verifica estatísticas dos reels"""
        try:
            # Busca informações do usuário
            user_info = self.client.account_info()
            
            # Busca todos os reels
            print_colored("📱 Buscando reels...", Fore.CYAN)
            reels = self.client.user_clips(str(user_info.pk), amount=0)
            
            if not reels:
                print_colored("ℹ️ Nenhum reel encontrado", Fore.BLUE)
                return
            
            # Estatísticas básicas
            total_reels = len(reels)
            total_likes = sum(reel.like_count for reel in reels)
            total_comments = sum(reel.comment_count for reel in reels)
            total_views = sum(reel.view_count for reel in reels if reel.view_count)
            
            # Reel mais antigo e mais recente
            oldest_reel = min(reels, key=lambda r: r.taken_at)
            newest_reel = max(reels, key=lambda r: r.taken_at)
            
            # Reel com mais likes
            most_liked = max(reels, key=lambda r: r.like_count)
            
            # Reel com mais visualizações
            most_viewed = max(reels, key=lambda r: r.view_count or 0)
            
            # Exibe relatório
            print_colored("\n" + "="*60, Fore.CYAN)
            print_colored("📊 ESTATÍSTICAS DOS SEUS REELS", Fore.CYAN)
            print_colored("="*60, Fore.CYAN)
            
            print_colored(f"👤 Usuário: @{user_info.username}", Fore.BLUE)
            print_colored(f"📱 Total de reels: {total_reels}", Fore.GREEN)
            print_colored(f"❤️ Total de likes: {total_likes:,}", Fore.RED)
            print_colored(f"💬 Total de comentários: {total_comments:,}", Fore.YELLOW)
            print_colored(f"👀 Total de visualizações: {total_views:,}", Fore.MAGENTA)
            
            if total_reels > 0:
                print_colored(f"📈 Média de likes por reel: {total_likes/total_reels:.1f}", Fore.GREEN)
                print_colored(f"📈 Média de comentários por reel: {total_comments/total_reels:.1f}", Fore.YELLOW)
                if total_views > 0:
                    print_colored(f"📈 Média de visualizações por reel: {total_views/total_reels:.1f}", Fore.MAGENTA)
            
            print_colored("\n" + "-"*60, Fore.CYAN)
            print_colored("🏆 DESTAQUES", Fore.CYAN)
            print_colored("-"*60, Fore.CYAN)
            
            print_colored(f"📅 Reel mais antigo: {oldest_reel.taken_at.strftime('%d/%m/%Y')}", Fore.BLUE)
            print_colored(f"📅 Reel mais recente: {newest_reel.taken_at.strftime('%d/%m/%Y')}", Fore.BLUE)
            
            print_colored(f"🔥 Reel com mais likes: {most_liked.like_count:,} likes", Fore.RED)
            if most_liked.caption_text:
                caption = most_liked.caption_text[:50] + "..." if len(most_liked.caption_text) > 50 else most_liked.caption_text
                print_colored(f"   Legenda: {caption}", Fore.WHITE)
            
            if most_viewed.view_count:
                print_colored(f"👁️ Reel com mais visualizações: {most_viewed.view_count:,} views", Fore.MAGENTA)
                if most_viewed.caption_text:
                    caption = most_viewed.caption_text[:50] + "..." if len(most_viewed.caption_text) > 50 else most_viewed.caption_text
                    print_colored(f"   Legenda: {caption}", Fore.WHITE)
            
            # Lista dos últimos 5 reels
            print_colored("\n" + "-"*60, Fore.CYAN)
            print_colored("📋 ÚLTIMOS 5 REELS", Fore.CYAN)
            print_colored("-"*60, Fore.CYAN)
            
            recent_reels = sorted(reels, key=lambda r: r.taken_at, reverse=True)[:5]
            
            for i, reel in enumerate(recent_reels, 1):
                date_str = reel.taken_at.strftime('%d/%m/%Y')
                caption = reel.caption_text[:40] + "..." if reel.caption_text and len(reel.caption_text) > 40 else reel.caption_text or "Sem legenda"
                
                print_colored(f"{i}. {date_str} - {reel.like_count:,} likes - {caption}", Fore.WHITE)
            
            print_colored("\n" + "="*60, Fore.CYAN)
            
        except Exception as e:
            print_colored(f"❌ Erro ao verificar reels: {e}", Fore.RED)
            self.logger.error(f"Erro ao verificar reels: {e}")


def main():
    """Função principal"""
    try:
        Config.validate()
        
        print_colored("🔍 Instagram Reels Checker", Fore.CYAN)
        print_colored("="*40, Fore.CYAN)
        
        # Cria instância do checker
        checker = InstagramChecker()
        
        # Faz login
        if not checker.login():
            print_colored("❌ Falha no login. Verifique suas credenciais.", Fore.RED)
            return
        
        # Verifica estatísticas
        checker.check_reels_stats()
        
    except KeyboardInterrupt:
        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
    except Exception as e:
        print_colored(f"❌ Erro crítico: {e}", Fore.RED)


if __name__ == "__main__":
    main()
