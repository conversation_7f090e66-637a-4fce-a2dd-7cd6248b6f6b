#!/usr/bin/env python3
"""
Script de configuração inicial para o Instagram Reels Archiver
"""

import os
import sys
import subprocess
from pathlib import Path
from colorama import Fore, Style, init

# Inicializa colorama
init(autoreset=True)

def print_colored(message: str, color: str = Fore.WHITE):
    """Imprime mensagem colorida"""
    print(f"{color}{message}{Style.RESET_ALL}")

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    if sys.version_info < (3, 7):
        print_colored("❌ Python 3.7 ou superior é necessário", Fore.RED)
        print_colored(f"Versão atual: {sys.version}", Fore.YELLOW)
        return False
    
    print_colored(f"✅ Python {sys.version.split()[0]} detectado", Fore.GREEN)
    return True

def install_dependencies():
    """Instala as dependências do projeto"""
    try:
        print_colored("📦 Instalando dependências...", Fore.CYAN)
        
        # Instala as dependências
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print_colored("✅ Dependências instaladas com sucesso!", Fore.GREEN)
            return True
        else:
            print_colored("❌ Erro ao instalar dependências:", Fore.RED)
            print_colored(result.stderr, Fore.RED)
            return False
            
    except Exception as e:
        print_colored(f"❌ Erro ao instalar dependências: {e}", Fore.RED)
        return False

def create_env_file():
    """Cria o arquivo .env se não existir"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print_colored("ℹ️ Arquivo .env já existe", Fore.BLUE)
        return True
    
    if not env_example.exists():
        print_colored("❌ Arquivo .env.example não encontrado", Fore.RED)
        return False
    
    try:
        # Copia o arquivo de exemplo
        with open(env_example, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print_colored("✅ Arquivo .env criado a partir do .env.example", Fore.GREEN)
        print_colored("⚠️ IMPORTANTE: Edite o arquivo .env com suas credenciais do Instagram", Fore.YELLOW)
        
        return True
        
    except Exception as e:
        print_colored(f"❌ Erro ao criar arquivo .env: {e}", Fore.RED)
        return False

def create_directories():
    """Cria diretórios necessários"""
    directories = ["backups", "logs"]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print_colored(f"📁 Diretório criado: {directory}/", Fore.BLUE)
        else:
            print_colored(f"📁 Diretório já existe: {directory}/", Fore.BLUE)

def show_next_steps():
    """Mostra os próximos passos para o usuário"""
    print_colored("\n" + "="*60, Fore.CYAN)
    print_colored("🎉 CONFIGURAÇÃO CONCLUÍDA!", Fore.CYAN)
    print_colored("="*60, Fore.CYAN)
    
    print_colored("\n📋 PRÓXIMOS PASSOS:", Fore.YELLOW)
    print_colored("1. Edite o arquivo .env com suas credenciais do Instagram:", Fore.WHITE)
    print_colored("   - INSTAGRAM_USERNAME=seu_usuario", Fore.WHITE)
    print_colored("   - INSTAGRAM_PASSWORD=sua_senha", Fore.WHITE)
    
    print_colored("\n2. Execute um dos comandos:", Fore.WHITE)
    print_colored("   python check_reels.py        # Verificar estatísticas", Fore.GREEN)
    print_colored("   python instagram_archiver.py # Arquivar reels", Fore.YELLOW)
    print_colored("   python unarchive_reels.py    # Desarquivar reels", Fore.BLUE)
    
    print_colored("\n⚠️ IMPORTANTE:", Fore.RED)
    print_colored("- Teste primeiro com DRY_RUN=true no arquivo .env", Fore.WHITE)
    print_colored("- Mantenha suas credenciais seguras", Fore.WHITE)
    print_colored("- Leia o README.md para mais informações", Fore.WHITE)

def main():
    """Função principal"""
    print_colored("🚀 Instagram Reels Archiver - Setup", Fore.CYAN)
    print_colored("="*50, Fore.CYAN)
    
    # Verifica versão do Python
    if not check_python_version():
        sys.exit(1)
    
    # Instala dependências
    if not install_dependencies():
        print_colored("❌ Falha na instalação das dependências", Fore.RED)
        sys.exit(1)
    
    # Cria arquivo .env
    if not create_env_file():
        print_colored("❌ Falha ao criar arquivo .env", Fore.RED)
        sys.exit(1)
    
    # Cria diretórios
    create_directories()
    
    # Mostra próximos passos
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n⏹️ Setup interrompido pelo usuário", Fore.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"\n❌ Erro durante o setup: {e}", Fore.RED)
        sys.exit(1)
