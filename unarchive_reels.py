#!/usr/bin/env python3
"""
Instagram Reels Unarchiver
Desarquiva reels que foram arquivados anteriormente
"""

import json
import time
import sys
from pathlib import Path
from typing import List

from instagrapi import Client
from tqdm import tqdm
from colorama import Fore

from config import Config
from utils import setup_logging, print_colored


class InstagramUnarchiver:
    """Classe para desarquivar reels do Instagram"""
    
    def __init__(self):
        self.client = Client()
        self.logger = setup_logging(Config.LOGS_DIR)
    
    def login(self) -> bool:
        """Faz login no Instagram"""
        try:
            print_colored("🔐 Fazendo login no Instagram...", Fore.CYAN)
            
            self.client.login(Config.INSTAGRAM_USERNAME, Config.INSTAGRAM_PASSWORD)
            
            user_info = self.client.account_info()
            print_colored(f"✅ Login realizado com sucesso! Usuário: @{user_info.username}", Fore.GREEN)
            self.logger.info(f"Login realizado para o usuário: {user_info.username}")
            
            return True
            
        except Exception as e:
            print_colored(f"❌ Erro no login: {e}", Fore.RED)
            self.logger.error(f"Erro no login: {e}")
            return False
    
    def unarchive_reel(self, media_id: str) -> bool:
        """Desarquiva um reel específico"""
        try:
            if Config.DRY_RUN:
                print_colored(f"🔍 [DRY RUN] Simulando desarquivamento do reel: {media_id}", Fore.YELLOW)
                return True
            
            result = self.client.media_unarchive(media_id)
            
            if result:
                print_colored(f"📤 Reel desarquivado: {media_id}", Fore.GREEN)
                self.logger.info(f"Reel desarquivado com sucesso: {media_id}")
            else:
                print_colored(f"⚠️ Falha ao desarquivar reel: {media_id}", Fore.YELLOW)
                self.logger.warning(f"Falha ao desarquivar reel: {media_id}")
            
            return result
            
        except Exception as e:
            print_colored(f"❌ Erro ao desarquivar reel {media_id}: {e}", Fore.RED)
            self.logger.error(f"Erro ao desarquivar reel {media_id}: {e}")
            return False
    
    def load_backup_file(self, backup_file: str) -> List[str]:
        """Carrega IDs dos reels de um arquivo de backup"""
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # Extrai os IDs dos reels
            reel_ids = [item['id'] for item in backup_data if item.get('product_type') == 'clips']
            
            print_colored(f"📄 Carregados {len(reel_ids)} reels do backup: {backup_file}", Fore.BLUE)
            return reel_ids
            
        except Exception as e:
            print_colored(f"❌ Erro ao carregar backup: {e}", Fore.RED)
            return []
    
    def unarchive_from_backup(self, backup_file: str):
        """Desarquiva reels usando um arquivo de backup"""
        reel_ids = self.load_backup_file(backup_file)
        
        if not reel_ids:
            print_colored("❌ Nenhum reel encontrado no backup", Fore.RED)
            return
        
        print_colored(f"🎯 Desarquivando {len(reel_ids)} reels...", Fore.CYAN)
        
        successful = 0
        failed = 0
        
        with tqdm(total=len(reel_ids), desc="Desarquivando reels") as pbar:
            for reel_id in reel_ids:
                try:
                    if self.unarchive_reel(reel_id):
                        successful += 1
                    else:
                        failed += 1
                    
                    # Delay entre requisições
                    if Config.DELAY_BETWEEN_REQUESTS > 0:
                        time.sleep(Config.DELAY_BETWEEN_REQUESTS)
                    
                    pbar.update(1)
                    
                except KeyboardInterrupt:
                    print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
                    break
        
        # Relatório final
        print_colored("\n" + "="*50, Fore.CYAN)
        print_colored("📊 RELATÓRIO FINAL", Fore.CYAN)
        print_colored("="*50, Fore.CYAN)
        print_colored(f"✅ Reels desarquivados com sucesso: {successful}", Fore.GREEN)
        print_colored(f"❌ Reels com falha: {failed}", Fore.RED)
        
        if Config.DRY_RUN:
            print_colored("🔍 Modo DRY RUN ativo - nenhum reel foi realmente desarquivado", Fore.YELLOW)


def list_backup_files():
    """Lista arquivos de backup disponíveis"""
    backup_dir = Path(Config.BACKUP_DIR)
    
    if not backup_dir.exists():
        print_colored("❌ Diretório de backup não encontrado", Fore.RED)
        return []
    
    backup_files = list(backup_dir.glob("reels_backup_*.json"))
    
    if not backup_files:
        print_colored("❌ Nenhum arquivo de backup encontrado", Fore.RED)
        return []
    
    # Ordena por data (mais recente primeiro)
    backup_files.sort(reverse=True)
    
    print_colored("\n📁 Arquivos de backup disponíveis:", Fore.CYAN)
    for i, backup_file in enumerate(backup_files, 1):
        print_colored(f"  {i}. {backup_file.name}", Fore.BLUE)
    
    return backup_files


def main():
    """Função principal"""
    try:
        Config.validate()
        
        print_colored("🔄 Instagram Reels Unarchiver", Fore.CYAN)
        print_colored("="*40, Fore.CYAN)
        
        if Config.DRY_RUN:
            print_colored("🔍 MODO DRY RUN ATIVO - Nenhum reel será realmente desarquivado", Fore.YELLOW)
        
        # Lista arquivos de backup
        backup_files = list_backup_files()
        if not backup_files:
            sys.exit(1)
        
        # Seleciona arquivo de backup
        try:
            choice = int(input(f"\n{Fore.YELLOW}Escolha o arquivo de backup (número): {Fore.WHITE}"))
            if choice < 1 or choice > len(backup_files):
                print_colored("❌ Escolha inválida", Fore.RED)
                sys.exit(1)
            
            selected_backup = backup_files[choice - 1]
            print_colored(f"📄 Arquivo selecionado: {selected_backup.name}", Fore.GREEN)
            
        except ValueError:
            print_colored("❌ Entrada inválida", Fore.RED)
            sys.exit(1)
        
        # Cria instância do unarchiver
        unarchiver = InstagramUnarchiver()
        
        # Faz login
        if not unarchiver.login():
            print_colored("❌ Falha no login. Verifique suas credenciais.", Fore.RED)
            sys.exit(1)
        
        # Confirma ação
        if not Config.DRY_RUN:
            response = input(f"\n{Fore.YELLOW}⚠️ Tem certeza que deseja desarquivar os reels do backup selecionado? (s/N): ")
            if response.lower() not in ['s', 'sim', 'y', 'yes']:
                print_colored("❌ Operação cancelada pelo usuário", Fore.YELLOW)
                sys.exit(0)
        
        # Desarquiva reels
        unarchiver.unarchive_from_backup(str(selected_backup))
        
        print_colored("\n🎉 Processo concluído!", Fore.GREEN)
        
    except KeyboardInterrupt:
        print_colored("\n⏹️ Processo interrompido pelo usuário", Fore.YELLOW)
        sys.exit(0)
    except Exception as e:
        print_colored(f"❌ Erro crítico: {e}", Fore.RED)
        sys.exit(1)


if __name__ == "__main__":
    main()
